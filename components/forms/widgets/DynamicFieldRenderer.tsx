"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// import { CKEditor } from "@ckeditor/ckeditor5-react";
// import { ClassicEditor } from "ckeditor5";
import { Field, Schema } from "@/lib/schema/types";
import { VirtualizedSelect } from "./VirtualizedSelect";
import { SimpleTextArray } from "./SimpleTextArray";
import { OneToManyManager } from "./OneToManyManager";
import { OneToOneManager } from "./OneToOneManager";
// import { TextArrayWidget } from "./TextArrayWidget";
import dynamic from "next/dynamic";
import "react-quill-new/dist/quill.snow.css";
const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });

type DynamicFieldRendererProps = {
  schema: Schema;
  item: any;
  itemIndex: number | null;
  fieldName: string;
  onUpdate: (updatedItem: any) => void;
  relatedSchemas?: Record<string, Schema>;
  parentId?: string;
  mode?: "create" | "edit";
}

export const DynamicFieldRenderer: React.FC<DynamicFieldRendererProps> = ({
  schema,
  item,
  itemIndex,
  fieldName,
  onUpdate,
  relatedSchemas = {},
  parentId,
  mode,
}) => {
  const { setValue } = useFormContext();

  if (!schema?.schema_definition?.fields) {
    return (
      <div className="text-sm text-muted-foreground">
        No fields defined for this schema
      </div>
    );
  }

  const handleFieldChange = (field: string, value: any) => {
    const updatedItem = { ...item, [field]: value };
    onUpdate(updatedItem);

    // Also update the form context with correct path
    // For one-to-one relationships, don't use array indexing
    const fieldPath = itemIndex !== null 
      ? `${fieldName}.${itemIndex}.${field}`
      : `${fieldName}.${field}`;
      
    setValue(fieldPath, value, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const renderField = (field: Field) => {
    // For one-to-one relationships, don't use array indexing
    const fieldKey = itemIndex !== null 
      ? `${fieldName}.${itemIndex}.${field.name}`
      : `${fieldName}.${field.name}`;
    const fieldValue = item[field.name] || "";

    // Skip hidden and auto-generated fields
    if (field.ui_config?.hidden || field.auto_generate) {
      return null;
    }

    // For auto-populated fields, show them as read-only if they have a value
    // Don't hide them completely as users should see what was auto-populated

    const label =
      field.ui_config?.label ||
      field.name
        ?.replace(/_/g, " ")
        ?.replace(/\b\w/g, (l: string) => l.toUpperCase());
    const placeholder =
      field.ui_config?.placeholder || `Enter ${label.toLowerCase()}`;
    const isRequired = field.required && !field.default;

    if (field.type === "text[]") {
      return (
        <div key={field.name} className="space-y-2 col-span-2">
          <Label htmlFor={fieldKey}>
            {label}
            {isRequired && <span className="text-destructive ml-1">*</span>}
          </Label>
          <SimpleTextArray
            value={Array.isArray(fieldValue) ? fieldValue : []}
            onChange={(value: string[]) => handleFieldChange(field.name, value)}
            placeholder={placeholder}
            disabled={false}
            maxItems={field.validation?.max_items || 50}
            helpText={field.ui_config?.help_text}
            widget={field.ui_config?.widget}
          />
        </div>
      );
    }

    switch (field.type) {
      case "text":
        if (field.ui_config?.widget === "rich_text") {
          return (
            <div key={field.name} className="space-y-2 col-span-2">
              <Label htmlFor={fieldKey}>
                {label}
                {isRequired && <span className="text-destructive ml-1">*</span>}
              </Label>
              <div className="border rounded-md border-gray-200">
                <ReactQuill
                  theme="snow"
                  value={fieldValue || ""}
                  onChange={(content, delta, source, editor) => {
                    handleFieldChange(field.name, content);
                  }}
                  onBlur={() => {}}
                  placeholder={placeholder || "Enter your content..."}
                  modules={{
                    toolbar: [
                      [{ header: [1, 2, 3, 4, 5, 6, false] }],
                      ["bold", "italic", "underline"],
                      [{ list: "ordered" }, { list: "bullet" }],
                      [{ indent: "-1" }, { indent: "+1" }],
                      ["link", "blockquote"],
                      ["clean"],
                    ],
                  }}
                />
              </div>
              {field.ui_config?.help_text && (
                <p className="text-xs text-muted-foreground">
                  {field.ui_config.help_text}
                </p>
              )}
            </div>
          );
        }
        if (field.foreign_key) {
          const isReferenceField =
            field.name.includes("_id") ||
            field.name === "reference_id" ||
            field.name === "parent_id";

          // If it's a reference field that should be auto-set, hide it
          if (isReferenceField && field.ui_config?.hidden) {
            return (
              <div key={field.name} className="space-y-2 hidden">
                <Input
                  id={fieldKey}
                  type="text"
                  hidden={true}
                  value="Will be set automatically"
                  onChange={(e) =>
                    handleFieldChange(field.name, e.target.value)
                  }
                  placeholder="Will be set automatically"
                  required={isRequired}
                />
              </div>
            );
          }

          // For regular foreign key fields, show the relationship selector
          return (
            <VirtualizedSelect
              key={`${fieldName}`}
              name={field.name}
              value={fieldValue}
              onChange={(value) => handleFieldChange(field.name, value)}
              placeholder={placeholder}
              required={isRequired}
              targetComponent={field.foreign_key.hasura_table}
              displayField={field.ui_config?.display_field}
              label={label}
              foreignKey={field.foreign_key}
              helpText={field.ui_config?.help_text}
            />
          );
        }

        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldKey}
              type="text"
              value={fieldValue}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={placeholder}
              required={isRequired}
              maxLength={field.validation?.max_length}
            />
            {field.ui_config?.help_text && (
              <p className="text-xs text-muted-foreground">
                {field.ui_config.help_text}
              </p>
            )}
          </div>
        );

      case "integer":
      case "decimal":
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldKey}
              type="number"
              value={fieldValue}
              onChange={(e) =>
                handleFieldChange(field.name, parseFloat(e.target.value) || 0)
              }
              placeholder={placeholder}
              required={isRequired}
              min={field.validation?.min}
              max={field.validation?.max}
              step={field.type === "decimal" ? "0.01" : "1"}
            />
            {field.ui_config?.help_text && (
              <p className="text-xs text-muted-foreground">
                {field.ui_config.help_text}
              </p>
            )}
          </div>
        );

      case "boolean":
        return (
          <div key={field.name} className="flex items-center space-x-2">
            <Checkbox
              id={fieldKey}
              checked={fieldValue || false}
              onCheckedChange={(checked) =>
                handleFieldChange(field.name, checked)
              }
            />
            <Label htmlFor={fieldKey} className="text-sm font-medium">
              {label}
            </Label>
            {field.ui_config?.help_text && (
              <p className="text-xs text-muted-foreground">
                {field.ui_config.help_text}
              </p>
            )}
          </div>
        );

      case "enum":
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select
              value={fieldValue}
              onValueChange={(value) => handleFieldChange(field.name, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {field.ui_config?.options?.map(
                  (option: { value: string; label: string }) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
            {field.ui_config?.help_text && (
              <p className="text-xs text-muted-foreground">
                {field.ui_config.help_text}
              </p>
            )}
          </div>
        );

      case "timestamp":
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldKey}
              type="datetime-local"
              value={
                fieldValue
                  ? new Date(fieldValue).toISOString().slice(0, 16)
                  : ""
              }
              onChange={(e) =>
                handleFieldChange(
                  field.name,
                  e.target.value ? new Date(e.target.value).toISOString() : ""
                )
              }
              required={isRequired}
            />
            {field.ui_config?.help_text && (
              <p className="text-xs text-muted-foreground">
                {field.ui_config.help_text}
              </p>
            )}
          </div>
        );

      case "uuid":
        // For foreign keys, we might want to show a relationship selector
        if (field.foreign_key) {
          const isReferenceField =
            field.name.includes("_id") ||
            field.name === "reference_id" ||
            field.name === "parent_id";
          const displayValue =
            fieldValue || (isReferenceField ? "Will be set automatically" : "");

          return (
            <div key={field.name} className="space-y-2">
              <Label htmlFor={fieldKey}>
                {label}
                {isRequired && <span className="text-destructive ml-1">*</span>}
              </Label>
              <Input
                id={fieldKey}
                type="text"
                value={displayValue}
                onChange={(e) => handleFieldChange(field.name, e.target.value)}
                placeholder={
                  isReferenceField
                    ? "Will be set automatically"
                    : "Enter ID or select from relationship"
                }
                required={isRequired}
                readOnly={isReferenceField && fieldValue}
                className={isReferenceField && !fieldValue ? "bg-muted" : ""}
              />
              <p className="text-xs text-muted-foreground">
                {isReferenceField
                  ? "Reference ID will be set automatically when parent record is saved"
                  : `Related to: ${field.foreign_key.hasura_table}`}
              </p>
            </div>
          );
        }

        // Handle auto-populated fields - show as read-only with the auto-populated value
        if (field.auto_populate && fieldValue) {
          return (
            <div key={field.name} className="space-y-2">
              <Label htmlFor={fieldKey}>
                {label}
                {isRequired && <span className="text-destructive ml-1">*</span>}
              </Label>
              <Input
                id={fieldKey}
                type="text"
                value={fieldValue}
                readOnly
                className="bg-muted"
              />
              <p className="text-xs text-muted-foreground">
                Auto-populated from parent context
              </p>
            </div>
          );
        }

        // For auto-generated UUIDs, show as read-only
        if (field.primary_key || field.auto_generate) {
          const displayValue =
            fieldValue ||
            (field.auto_generate ? "Will be auto-generated" : "Auto-generated");
          return (
            <div key={field.name} className="space-y-2">
              <Label htmlFor={fieldKey}>{label}</Label>
              <Input
                id={fieldKey}
                type="text"
                value={displayValue}
                readOnly
                className="bg-muted"
              />
              {field.auto_generate && !fieldValue && (
                <p className="text-xs text-muted-foreground">
                  ID will be generated automatically when saved
                </p>
              )}
            </div>
          );
        }

        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldKey}
              type="text"
              value={fieldValue}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={placeholder}
              required={isRequired}
            />
          </div>
        );

      default:
        return (
          <div key={field.name} className="space-y-2">
            <Label htmlFor={fieldKey}>
              {label}
              {isRequired && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldKey}
              type="text"
              value={fieldValue}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={placeholder}
              required={isRequired}
            />
            <p className="text-xs text-muted-foreground">
              Field type: {field.type}
            </p>
          </div>
        );
    }
  };

  // Determine grid layout based on number of fields
  const visibleFields = schema.schema_definition.fields.filter(
    (field: any) => !field.ui_config?.hidden && !field.auto_generate
  );

  const getGridCols = () => {
    if (visibleFields.length === 2) return "grid-cols-2";
    if (visibleFields.length <= 4) return "grid-cols-2";
    return "grid-cols-3";
  };

  // Render nested relationships
  const renderNestedRelationships = () => {
    if (!schema.relationships || schema.relationships.length === 0) {
      return null;
    }

    return (
      <div className="mt-6 space-y-4">
        {schema.relationships.map((relationship) => {
          const relatedSchema = relatedSchemas[relationship.target_component];
          if (!relatedSchema) {
            return null;
          }

          const relationshipConfig = {
            name: relationship.name,
            type: relationship.type,
            targetComponent: relationship.target_component,
            title: relationship.name.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
            required: relationship.required || false,
            allowCreate: relationship.config?.allow_create !== false,
            allowEdit: relationship.config?.allow_edit !== false,
            allowDelete: relationship.config?.allow_delete !== false,
            sortable: relationship.config?.sortable,
            maxItems: relationship.config?.max_items,
            sourceField: relationship.source_field,
            displayField: relationship.config?.display_field,
          };

          // Render nested relationships directly using the managers
          // Construct the correct nested field path
          const nestedFieldName = itemIndex !== undefined
            ? `${fieldName}.${itemIndex}.${relationship.name}`
            : relationship.name;

          // For nested relationships, the parentId should be the ID of the current item
          // not the original parent. This ensures proper auto-population in deeply nested forms.
          const currentItemId = item?.id || parentId;

          if (relationship.type === "one-to-many") {
            return (
              <div key={relationship.name} className="border-l-2 border-blue-200 pl-4 ml-2">
                <OneToManyManager
                  relationship={relationshipConfig}
                  fieldName={nestedFieldName}
                  relatedSchema={relatedSchema}
                  className="mb-6"
                  parentId={currentItemId}
                  relatedSchemas={relatedSchemas}
                />
              </div>
            );
          }

          if (relationship.type === "one-to-one") {
            return (
              <div key={relationship.name} className="border-l-2 border-green-200 pl-4 ml-2">
                <OneToOneManager
                  relationship={relationshipConfig}
                  fieldName={nestedFieldName}
                  relatedSchema={relatedSchema}
                  className="mb-6"
                  parentId={currentItemId}
                  relatedSchemas={relatedSchemas}
                />
              </div>
            );
          }

          return null;
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className={`grid ${getGridCols()} gap-4`}>
        {schema.schema_definition.fields.map(renderField)}
      </div>
      {renderNestedRelationships()}
    </div>
  );
};
